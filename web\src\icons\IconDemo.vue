<template>
  <div class="icon-demo">
    <h1>图标系统演示</h1>
    
    <!-- 基础使用 -->
    <section class="demo-section">
      <h2>基础使用</h2>
      <div class="icon-grid">
        <div v-for="iconName in basicIcons" :key="iconName" class="icon-item">
          <Icon :name="iconName" size="24" />
          <span class="icon-name">{{ iconName }}</span>
        </div>
      </div>
    </section>
    
    <!-- 尺寸演示 -->
    <section class="demo-section">
      <h2>不同尺寸</h2>
      <div class="size-demo">
        <Icon name="Search24Filled" size="16" />
        <Icon name="Search24Filled" size="20" />
        <Icon name="Search24Filled" size="24" />
        <Icon name="Search24Filled" size="32" />
        <Icon name="Search24Filled" size="48" />
      </div>
    </section>
    
    <!-- 颜色演示 -->
    <section class="demo-section">
      <h2>不同颜色</h2>
      <div class="color-demo">
        <Icon name="Star48Filled" size="24" color="#1890ff" />
        <Icon name="Star48Filled" size="24" color="#52c41a" />
        <Icon name="Star48Filled" size="24" color="#fa8c16" />
        <Icon name="Star48Filled" size="24" color="#f5222d" />
        <Icon name="Star48Filled" size="24" color="#722ed1" />
      </div>
    </section>
    
    <!-- 可点击图标 -->
    <section class="demo-section">
      <h2>可点击图标</h2>
      <div class="clickable-demo">
        <Icon 
          name="LikeOutlined"
          size="24" 
          :color="liked ? '#f5222d' : '#999'" 
          clickable 
          @click="liked = !liked" 
        />
        <span>{{ liked ? '已点赞' : '点赞' }}</span>
      </div>
    </section>
    
    <!-- 兼容性组件演示 -->
    <section class="demo-section">
      <h2>兼容vicons使用</h2>
      <div class="compat-demo">
        <div class="compat-item">
          <VIcon library="fluent" iconName="TextBold20Filled" size="20" />
          <span>TextBold20Filled (fluent)</span>
        </div>
        <div class="compat-item">
          <VIcon library="antd" iconName="LikeOutlined" size="20" />
          <span>LikeOutlined (antd)</span>
        </div>
        <div class="compat-item">
          <VIcon library="ionicons4" iconName="IosCode" size="20" />
          <span>IosCode (ionicons4)</span>
        </div>
        <div class="compat-item">
          <VIcon library="tabler" iconName="Blockquote" size="20" />
          <span>Blockquote (tabler)</span>
        </div>
      </div>
    </section>
    
    <!-- TS/JS使用演示 -->
    <section class="demo-section">
      <h2>TS/JS中使用</h2>
      <div class="js-demo">
        <button @click="addIconToContainer">添加图标到容器</button>
        <div ref="iconContainer" class="icon-container"></div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Icon from './Icon.vue';
// import VIcon from './VIcon.vue'; // VIcon 已移除
import { createIconElement, getIconHtml, type IconName } from './index';

const liked = ref(false);
const iconContainer = ref<HTMLElement>();

const basicIcons: IconName[] = [
  'Search24Filled', 'Add24Filled', 'IosClose', 'Star48Filled', 'CommentNote20Filled',
  'TextBold20Filled', 'TextItalic20Filled', 'TextUnderline24Filled',
  'ArrowUndo16Filled', 'ArrowRedo16Filled', 'ArrowRight20Filled',
  'TextAlignLeft24Filled', 'TextAlignCenter24Filled', 'TextAlignRight24Filled',
  'TextBulletListLtr16Filled', 'TextNumberListLtr16Filled', 'TaskListLtr24Filled'
];

const addIconToContainer = () => {
  if (!iconContainer.value) return;
  
  // 方式1: 使用createIconElement
  const iconElement = createIconElement('Star48Filled', {
    size: '20px',
    color: '#1890ff',
    className: 'dynamic-icon'
  });

  // 方式2: 使用getIconHtml
  const iconHtml = getIconHtml('Add24Filled', { size: 16, color: '#52c41a' });
  
  iconContainer.value.appendChild(iconElement);
  iconContainer.value.innerHTML += iconHtml;
};
</script>

<style scoped>
.icon-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  margin-bottom: 16px;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.icon-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.icon-name {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.size-demo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.color-demo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.clickable-demo {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.compat-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.compat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.js-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.js-demo button {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: fit-content;
}

.js-demo button:hover {
  background: #40a9ff;
}

.icon-container {
  min-height: 60px;
  padding: 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

:deep(.dynamic-icon) {
  margin-right: 8px;
}
</style>
