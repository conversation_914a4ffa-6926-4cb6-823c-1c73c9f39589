<template>
  <div class="search-container">
    <!-- 搜索框，用于输入搜索关键词 -->
    <NInput
      style="border-radius: 0.5rem"
      v-model:value="searchCondition.searchKey"
      size="large"
      :placeholder="placeholder"
      clearable
      @input="searchByCondition"
    >
      <template #suffix>
        <Icon name="Search24Filled" />
      </template>
    </NInput>
    <!-- 条件切换tabs -->
    <NTabs :value="activeTabKey" justify-content="space-evenly">
      <NTab
        v-for="(tab, index) in searchTabs"
        :key="index"
        :name="tab.name"
        :label="tab.label"
        @click="searchByTab(tab.name)"
      />
    </NTabs>
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';


import { NInput, NIcon, NTabs, NTab } from 'naive-ui'
import { ref, watch } from 'vue'

import { HOME_SEARCH_CONDITION } from '@/constants/storage.constants'
import type { SearchCondition } from '@/types/search.types'
import localStorage from '@/utils/local-storage'

const props = defineProps({
  placeholder: {
    type: String,
    default: '感兴趣的内容',
  },
  modelValue: {
    type: Object as () => SearchCondition,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'search'])

// 创建搜索条件的本地引用
const searchCondition = ref<SearchCondition>({ ...props.modelValue })

// 当外部值变化时更新本地值
watch(
  () => props.modelValue,
  (newVal) => {
    searchCondition.value = { ...newVal }
    // 当外部值变化时，重新设置活动标签
    setActiveTabFromCondition()
  },
  { deep: true },
)

// 更新外部值
const updateModelValue = () => {
  emit('update:modelValue', { ...searchCondition.value })
}

// 搜索标签
const activeTabKey = ref('')
const searchTabs = [
  { name: 'owner', label: '我的' },
  { name: 'interaction', label: '互动' },
  { name: 'favorite', label: '收藏' },
]

// 根据搜索条件设置当前活动标签
const setActiveTabFromCondition = () => {
  activeTabKey.value = ''
  for (const tab of searchTabs) {
    if (searchCondition.value[tab.name]) {
      activeTabKey.value = tab.name
      break
    }
  }
}

// 初始化时设置活动标签
setActiveTabFromCondition()

// 条件搜索
const searchByCondition = () => {
  updateModelValue()
  saveSearchCondition()
  emit('search')
}

// 标签搜索
const searchByTab = (tabName: string) => {
  if (activeTabKey.value === tabName) {
    activeTabKey.value = ''
  } else {
    activeTabKey.value = tabName
  }

  searchCondition.value[tabName] = !searchCondition.value[tabName]
  Object.keys(searchCondition.value).forEach((key) => {
    if (key !== tabName && key !== 'searchKey' && key !== 'tag') searchCondition.value[key] = false
  })

  updateModelValue()
  saveSearchCondition()
  emit('search')
}

// 保存搜索条件到localStorage
const saveSearchCondition = () => {
  localStorage.set(HOME_SEARCH_CONDITION, searchCondition.value)
}
</script>

<style scoped lang="scss">
.search-container {
  margin-left: 10%;
  margin-right: 10%;
  margin-bottom: -2rem;
  width: 31rem;
}
</style>
