<template>
  <div class="user-info-group">
    <div class="online-notification-container">
      <NotificationBtnModal @locationComment="handleLocationComment" />
      <div class="online-info">
        {{ onlineCount }}<Icon name="UserMultiple" size="20" />
      </div>
    </div>
    <UserAvatar />
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue'
import { NIcon } from 'naive-ui'
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

import userApi from '@/api/user'
import NotificationBtnModal from '@/components/NotificationBtnModal.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import type { ResponseData } from '@/types/response_data.types'

// 定义要触发的事件
const emit = defineEmits<{
  (e: 'locationComment', commentId: string): void
}>()
onMounted(() => {
  loadOnlineCount()
})
const route = useRoute()
watch(route, () => {
  loadOnlineCount()
})

const onlineCount = ref<number>(0)
const loadOnlineCount = () => {
  userApi.online().then((res: ResponseData) => {
    if (res?.data) {
      onlineCount.value = res.data
    }
  })
}
// 处理定位评论事件
const handleLocationComment = (commentId: string) => {
  emit('locationComment', commentId)
}
</script>

<style scoped>
.user-info-group {
  display: flex;
  align-items: center;
  margin-top: 0.75rem;
}

.online-notification-container {
  width: 3rem;
  margin-right: 12%;
}

.online-info {
  display: flex;
  justify-content: flex-end;
}
</style>
