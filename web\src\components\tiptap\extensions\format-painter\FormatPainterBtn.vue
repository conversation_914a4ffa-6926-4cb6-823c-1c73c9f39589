<template>
  <LongPress :duration="500" @long-press="clearFormat" @click="handleClick">
    <TiptapBtn
      :is-active="editor?.storage?.formatPainter?.isActive ?? false"
      :icon="() => h(Icon, { name: 'FormatPainterOutlined' })"
      :tooltip="editor?.storage?.formatPainter?.isActive ? '点击应用格式' : '格式刷'"
      :show="true"
      :trigger="() => {}"
      :disabled="!hasSelection"
      @mousedown.prevent
      @click.prevent
    />
  </LongPress>
</template>

<script setup lang="ts">
import Icon from '@/icons/Icon.vue';


import { computed, onMounted, onUnmounted, h } from 'vue'

import LongPress from '@/components/LongPress.vue'
import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'

import type { Node, Mark } from '@tiptap/pm/model'
import type { Transaction } from '@tiptap/pm/state'
import type { Editor } from '@tiptap/vue-3'

interface EditorInstance extends Editor {
  storage: Editor['storage'] & {
    formatPainter: {
      isActive: boolean
      sourceNode: Node | null
      sourceMarks: Mark[] | null
    }
  }
}

const props = defineProps<{
  editor: EditorInstance
}>()

const hasSelection = computed(() => {
  const { selection } = props.editor?.state ?? {}
  if (!selection) return false
  return selection.from !== selection.to
})

// 清除格式的通用函数
const clearFormat = () => {
  const { selection } = props.editor?.state
  const { from, to } = selection

  if (from !== to) {
    const tr = props.editor.state.tr as Transaction

    // 清除目标范围内的所有标记
    props.editor.state.doc.nodesBetween(from, to, (node: Node, pos: number) => {
      if (node.isText) {
        tr.removeMark(pos, pos + node.nodeSize, null)
      }
    })

    props.editor.view.dispatch(tr)
    props.editor.commands.focus()
  }
}

const handleClick = () => {
  const { formatPainter } = props.editor?.storage
  const { selection } = props.editor?.state
  const { from, to } = selection

  if (!formatPainter.isActive) {
    // 第一次点击：保存源格式
    const node = props.editor.state.doc.nodeAt(from) as Node
    if (node) {
      // 设置激活状态
      formatPainter.isActive = true
      formatPainter.sourceNode = node
      formatPainter.sourceMarks = [...node.marks]

      // 取消选中状态
      props.editor.commands.focus()
    }
  } else {
    // 第二次点击：应用格式
    if (formatPainter.sourceMarks) {
      // 只有在有文本被选中时才应用格式
      if (from !== to) {
        const tr = props.editor.state.tr as Transaction

        // 清除目标范围内的所有标记
        props.editor.state.doc.nodesBetween(from, to, (node: Node, pos: number) => {
          if (node.isText) {
            tr.removeMark(pos, pos + node.nodeSize, null)
          }
        })

        // 应用源格式的标记
        formatPainter.sourceMarks.forEach((mark: Mark) => {
          tr.addMark(from, to, mark)
        })

        // 取消选中状态
        props.editor.view.dispatch(tr)
        props.editor.commands.focus()

        // 应用格式后重置状态
        formatPainter.isActive = false
        formatPainter.sourceNode = null
        formatPainter.sourceMarks = null
      }
      // 如果没有选中文本，保持激活状态
    }
  }
}

// 监听选择变化
onMounted(() => {
  if (props.editor) {
    props.editor.on('selectionUpdate', () => {
      const { formatPainter } = props.editor?.storage || {}
      const { selection } = props.editor?.state || {}
      const { from, to } = selection || {}

      // 如果未选中文本且处于激活状态，保持激活状态
      if (from === to && formatPainter?.isActive) {
        const tr = props.editor.state.tr as Transaction
        tr.setMeta('addToHistory', false)
        props.editor.view.dispatch(tr)
      }
    })
  }
})

onUnmounted(() => {
  if (props.editor) {
    props.editor.off('selectionUpdate')
  }
})
</script>
