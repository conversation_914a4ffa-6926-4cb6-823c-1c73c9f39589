<template>
  <NCard
    class="card-item cursor-pointer"
    :class="cardClasses"
    :data-article-id="article.id"
    @click.ctrl="handleArticleClick"
    header-style="padding-bottom:0.25rem;border-bottom: var(--border-1);"
    :style="cardStyle"
  >
    <template #header>
      <div class="article-header">
        <NTooltip v-if="article.isOwner">
          <template #trigger>
            <div class="scope-icon-wrapper clickable" @click.stop="$emit('toggleScope', article)">
              <Icon
                :name="article.publishedScope == ArticlePublishedScope.PERSONAL ? 'LockOutlined' : 'UnlockOutlined'"
                size="18"
              />
            </div>
          </template>
          {{ scopeTooltipText }}
        </NTooltip>

        <div class="article-title" @click.stop="handleArticleClick">
          {{ article.title }}
        </div>
      </div>
    </template>

    <template #header-extra>
      <NAvatar
        round
        :size="45"
        :src="article.publisherAvatar"
        object-fit="cover"
        class="article-avatar"
        @mousedown.stop="handleAvatarMouseDown"
        @touchstart.stop="handleAvatarTouchStart"
        @mouseup.stop="$emit('cancelLongPress')"
        @mouseleave.stop="$emit('cancelLongPress')"
        @touchcancel.stop="$emit('cancelLongPress')"
        @contextmenu.prevent
      />
    </template>

    <div class="flex-between-center">
      <div>
        <NTag type="primary" class="card-tag" v-for="item in article.tags" :key="item">{{
          item
        }}</NTag>
      </div>
      <div>
        <NIcon size="24" class="cursor-pointer" @click.stop="$emit('download', article.id)">
          <DocumentDownload />
        </NIcon>
      </div>
    </div>

    <div class="article-content">
      <NScrollbar style="padding-right: 0.5rem">
        <TiptapEditor
          :ref="(el: any) => el && $emit('setEditor', article.id, el)"
          v-model="article.contentObj"
          :editable="false"
          :file-bucket="ARTICLE"
          :all-extensions="true"
          :character-limit="ARTICLE_CHARACTER_LIMIT"
        />
      </NScrollbar>
    </div>
  </NCard>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';



import { NCard, NTag, NAvatar, NScrollbar, NIcon, NTooltip } from 'naive-ui'
import { computed } from 'vue'

import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { ArticlePublishedScope } from '@/constants/article_published_scope.constants'
import { ARTICLE } from '@/constants/bucket.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap.constants'
import router from '@/router'

const props = defineProps({
  article: { type: Object, required: true },
  index: { type: Number, required: true },
  cardColor: { type: String, required: true },
  isDragging: { type: Boolean, default: false },
  draggedArticle: { type: Object, default: null },
  dragOverCardId: { type: String, default: null },
  dragOverPosition: { type: String, default: null },
  isSingleCardRow: { type: Boolean, default: false },
  dragStyle: { type: Object, default: () => ({}) },
})

const emit = defineEmits([
  'toggleScope',
  'startLongPress',
  'cancelLongPress',
  'download',
  'setEditor',
])

// 计算属性
const cardClasses = computed(() => {
  const isDraggedCard = props.isDragging && props.draggedArticle?.id === props.article.id
  const isDragOverCard = props.isDragging && props.dragOverCardId === props.article.id

  return {
    dragging: isDraggedCard,
    'drag-over-before':
      isDragOverCard && props.dragOverPosition === 'before' && !props.isSingleCardRow,
    'drag-over-after':
      isDragOverCard && props.dragOverPosition === 'after' && !props.isSingleCardRow,
    'drag-over-before-vertical':
      isDragOverCard && props.dragOverPosition === 'before' && props.isSingleCardRow,
    'drag-over-after-vertical':
      isDragOverCard && props.dragOverPosition === 'after' && props.isSingleCardRow,
  }
})

const cardStyle = computed(() => ({
  backgroundColor: props.cardColor,
  ...(props.isDragging && props.draggedArticle?.id === props.article.id ? props.dragStyle : {}),
}))

const scopeTooltipText = computed(() => {
  const isPersonal = props.article.publishedScope === ArticlePublishedScope.PERSONAL
  return props.article.isOwner
    ? `点击切换为${isPersonal ? '公开' : '个人'}可见`
    : `${isPersonal ? '个人' : '公开'}可见`
})

// 事件处理
const handleArticleClick = () => {
  const route = router.resolve({ name: 'Article', params: { articleId: props.article.id } })
  const newWindow = window.open(route.href, '_blank')
  newWindow?.focus()
}

const handleAvatarMouseDown = (event: MouseEvent) => {
  emit('startLongPress', event, props.article, event.currentTarget as HTMLElement)
}

const handleAvatarTouchStart = (event: TouchEvent) => {
  emit('startLongPress', event, props.article, event.currentTarget as HTMLElement)
}
</script>

<style scoped lang="scss">
.card-item {
  border-radius: 0.5rem;
  margin-top: 1.25rem;
  box-sizing: border-box;
  max-width: 100vw;

  &:hover {
    transform: translateY(-0.6rem);
    box-shadow: var(--shadow);
  }

  &.dragging {
    opacity: 0.3;
    pointer-events: none;
  }

  // 拖拽指示器
  &.drag-over-before::before,
  &.drag-over-after::after,
  &.drag-over-before-vertical::before,
  &.drag-over-after-vertical::after {
    content: '';
    position: absolute;
    background-color: var(--blue);
    z-index: 10;
    border-radius: 2px;
  }

  // 水平指示器（多卡片行）
  &.drag-over-before::before,
  &.drag-over-after::after {
    top: 0;
    bottom: 0;
    width: 4px;
  }

  &.drag-over-before::before {
    left: -12px;
  }
  &.drag-over-after::after {
    right: -12px;
  }

  // 垂直指示器（单卡片行/窄屏）
  &.drag-over-before-vertical::before,
  &.drag-over-after-vertical::after {
    left: 0;
    right: 0;
    height: 4px;
  }

  &.drag-over-before-vertical::before {
    top: -12px;
  }
  &.drag-over-after-vertical::after {
    bottom: -12px;
  }

  .article-header {
    display: flex;
    align-items: center;
    padding: 0.15rem 0;

    .scope-icon-wrapper {
      margin-right: 0.6rem;
      padding: 0.3rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      flex-shrink: 0;
      color: var(--black);
      cursor: pointer;

      &:hover {
        opacity: 0.8;
        transform: scale(1.1);
      }
    }

    .article-title {
      cursor: pointer;
      font-size: 1.1rem;
      font-weight: bold;
      color: var(--black);
      transition: color 0.2s ease;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        color: var(--blue);
        text-decoration: underline;
      }
    }
  }

  .card-tag {
    margin: 0.6rem 0.1rem;
  }

  .article-content {
    border-radius: 0.25rem;
    padding: 0.25rem;
    height: 19rem;
    background-color: var(--white-1);

    :deep(.image-wrapper),
    :deep(img) {
      max-width: 100%;
      height: auto !important;
      object-fit: contain;
    }

    @media (width <= 768px) {
      :deep(.ProseMirror) {
        p > .image-wrapper,
        p > img {
          max-width: 100% !important;
          min-width: unset !important;
          width: auto !important;
        }
      }
    }
  }

  .article-avatar {
    cursor: grab;
    user-select: none;
    -webkit-user-select: none;
    touch-action: none;

    &:active {
      cursor: grabbing;
    }
  }
}
</style>
