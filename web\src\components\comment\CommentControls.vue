<template>
  <div class="comment-controls-container">
    <div class="comment-reply-info">
      <NButton
        class="comment-reply-list-btn"
        v-show="showReplyListBtn && !comment.fixed"
        style="margin-left: 3%"
        text
        type="info"
        @click="$emit('showReplyList', comment)"
      >
        回复列表>
      </NButton>
      <Icon name="CommentNote20Filled" />
      {{ comment.replyCount }}
    </div>
    <div class="comment-interaction-btn">
      <Icon name="LikeOutlined" />
      {{ comment.likeCount }}
      <Icon name="DislikeOutlined" />
      {{ comment.dislikeCount }}
      <Icon name="Star48Filled" />
      {{ comment.favoriteCount }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';



import { NButton, NIcon } from 'naive-ui'

import type { Comment } from '@/types/comment.types'

const props = defineProps<{
  comment: Comment
  showReplyListBtn: boolean
}>()

const emit = defineEmits<{
  (e: 'showReplyList', comment: Comment): void
  (e: 'handleCommentReplyClick', comment: Comment): void
  (e: 'interactionBtn', comment: Comment, actionType: number): void
  (e: 'favoriteBtn', comment: Comment): void
}>()
</script>

<style scoped lang="scss">
.comment-controls-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;

  .comment-reply-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .comment-interaction-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  // 统一交互按钮样式
  .n-icon {
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }
  }

  // 按钮样式
  .comment-reply-list-btn {
    font-size: 0.9rem;
    padding: 0 0.5rem;
  }
}
</style>
