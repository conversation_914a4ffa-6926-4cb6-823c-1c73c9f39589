<template>
  <Icon name="Add24Filled" />
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';


import { NIcon } from 'naive-ui'
import { ref, onMounted, onUnmounted } from 'vue'

const emit = defineEmits(['click'])

// 按钮旋转状态
const isRotating = ref(false)
const isAutoRotating = ref(false)
const rotationTimer = ref<number | null>(null)
const autoRotationTimer = ref<number | null>(null)

// 处理按钮点击
const handleClick = () => {
  emit('click')
}

// 处理鼠标悬浮，触发旋转
const handleMouseEnter = () => {
  // 如果已经在旋转中，不需要再次触发
  if (isRotating.value) return

  // 清除任何即将到来的自动旋转
  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }

  // 设置旋转状态
  isRotating.value = true

  // 设置一个计时器，在旋转动画完成后重置状态
  rotationTimer.value = window.setTimeout(() => {
    isRotating.value = false
    // 重新开始自动旋转计时
    scheduleNextAutoRotation()
  }, 1500) // 动画持续时间
}

// 处理鼠标离开
const handleMouseLeave = () => {
  // 不立即停止旋转，让当前旋转完成
  // 只有在没有正在进行的旋转时才计划下一次自动旋转
  if (!isRotating.value) {
    scheduleNextAutoRotation()
  }
}

// 计划下一次自动旋转
const scheduleNextAutoRotation = () => {
  // 清除任何现有的自动旋转计时器
  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }

  // 设置一个随机时间（5-15秒）后触发自动旋转
  const nextRotationTime = 5000 + Math.random() * 10000
  autoRotationTimer.value = window.setTimeout(() => {
    triggerAutoRotation()
  }, nextRotationTime)
}

// 触发自动旋转
const triggerAutoRotation = () => {
  // 如果已经在旋转或者用户鼠标悬浮中，不执行自动旋转
  if (isRotating.value) {
    scheduleNextAutoRotation()
    return
  }

  // 设置旋转状态
  isRotating.value = true

  // 旋转完成后重置状态
  rotationTimer.value = window.setTimeout(() => {
    isRotating.value = false
    // 计划下一次自动旋转
    scheduleNextAutoRotation()
  }, 1500) // 动画持续时间
}

onMounted(() => {
  // 开始自动旋转计时
  scheduleNextAutoRotation()
})

onUnmounted(() => {
  // 清除所有计时器
  if (rotationTimer.value) {
    clearTimeout(rotationTimer.value)
    rotationTimer.value = null
  }

  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }
})
</script>

<style scoped lang="scss">
.create-button {
  transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1); /* 使用回弹效果的贝塞尔曲线 */
  transform-origin: center;
  will-change: transform;

  &.is-rotating {
    animation: rotate-and-scale 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  }
}

@keyframes rotate-and-scale {
  0% {
    transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(1.5);
  }

  100% {
    transform: rotate(360deg) scale(1);
  }
}
</style>
