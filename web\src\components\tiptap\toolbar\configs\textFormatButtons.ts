import Icon from '@/icons/Icon.vue'
import { h } from 'vue'

import type { ToolbarButtonConfig } from './types'

/**
 * 文本格式按钮配置
 */
export const textFormatButtons: ToolbarButtonConfig[] = [
  {
    icon: () => h(Icon, { name: 'TextBold20Filled' }),
    extensionName: 'bold',
    trigger: (editor) => editor?.chain().focus().toggleBold().run(),
    isActive: (editor) => editor?.isActive('bold'),
    tooltip: '加粗',
  },
  {
    icon: () => h(Icon, { name: 'TextItalic20Filled' }),
    extensionName: 'italic',
    trigger: (editor) => editor?.chain().focus().toggleItalic().run(),
    isActive: (editor) => editor?.isActive('italic'),
    tooltip: '斜体',
  },
  {
    icon: () => h(Icon, { name: 'TextStrikethrough20Filled' }),
    extensionName: 'strike',
    trigger: (editor) => editor?.chain().focus().toggleStrike().run(),
    isActive: (editor) => editor?.isActive('strike'),
    tooltip: '删除线',
  },
  {
    icon: () => h(Icon, { name: 'TextUnderline24Filled' }),
    extensionName: 'underline',
    trigger: (editor) => editor?.chain().focus().toggleUnderline().run(),
    isActive: (editor) => editor?.isActive('underline'),
    tooltip: '下划线',
  },
  {
    icon: () => h(Icon, { name: 'IosCode' }),
    extensionName: 'code',
    trigger: (editor) => editor?.chain().focus().toggleCode().run(),
    isActive: (editor) => editor?.isActive('code'),
    tooltip: '行内代码',
  },
]
