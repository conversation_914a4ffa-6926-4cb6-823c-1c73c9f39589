import Icon from '@/icons/Icon.vue'
import { h } from 'vue'

import type { ToolbarButtonConfig } from './types'

/**
 * 对齐按钮配置
 */
export const alignButtons: ToolbarButtonConfig[] = [
  {
    icon: () => h(Icon, { name: 'TextAlignLeft24Filled' }),
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('left').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'left' }),
    tooltip: '左对齐',
  },
  {
    icon: () => h(Icon, { name: 'TextAlignCenter24Filled' }),
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('center').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'center' }),
    tooltip: '居中对齐',
  },
  {
    icon: () => h(Icon, { name: 'TextAlignRight24Filled' }),
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('right').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'right' }),
    tooltip: '右对齐',
  },
  {
    icon: () => h(Icon, { name: 'TextAlignJustify24Filled' }),
    extensionName: 'align',
    trigger: (editor) => editor?.chain().focus().setTextAlign('justify').run(),
    isActive: (editor) => editor?.isActive({ textAlign: 'justify' }),
    tooltip: '两端对齐',
  },
]
