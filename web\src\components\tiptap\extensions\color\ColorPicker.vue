<template>
  <TiptapBtn :icon="icon" :show="show" :trigger="() => trigger(type)" :is-active="isActiveStyle">
    <template #content>
      <NColorPicker
        :show-preview="false"
        size="small"
        placement="top"
        to="body"
        v-model:value="colorValue"
        :popover-style="popoverStyle"
        :render-label="renderLabel"
        :swatches="swatches"
        :show="isVisible"
        @update:value="handleColorChange"
      >
      </NColorPicker>
    </template>
  </TiptapBtn>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';


import { NColorPicker } from 'naive-ui'
import { ref, computed, onUnmounted, h } from 'vue'

import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'
import { COLOR_PICKER } from '@/constants/frequency_key.constants'
import frequencyLimit from '@/utils/frequency-limit'

const props = defineProps({
  show: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    required: true,
  },
  editor: {
    type: Object,
    required: true,
  },
  colorType: {
    type: String,
    default: 'color', // 'color' 或 'backgroundColor'
  },
})

const icon = computed(() => {
  return () => h(Icon, {
    name: props.colorType === 'color' ? 'TextColor24Regular' : 'Color24Regular'
  })
})

const isActiveStyle = computed(() => {
  if (!props.editor) return false

  try {
    if (props.colorType === 'color') {
      // 检查textStyle标记是否激活并有color属性
      const hasColor = props.editor.isActive('textStyle')
      const textStyleAttrs = props.editor.getAttributes('textStyle')
      return hasColor && !!textStyleAttrs.color
    } else {
      // 检查highlight标记是否激活
      return props.editor.isActive('highlight')
    }
  } catch (error) {
    console.error('Error checking active style:', error)
    return false
  }
})

const colorState = ref({
  visible: false,
  value: '#000000FF',
  type: '',
})

const colorValue = computed({
  get: () => colorState.value.value,
  set: (val) => {
    colorState.value.value = val
  },
})

const isVisible = computed(() => colorState.value.visible && colorState.value.type === props.type)

const renderLabel = () => ''
const swatches = ['#FFFFFF', '#18A058', '#2080F0', '#F0A020', 'rgba(208, 48, 80, 1)']
const popoverStyle = 'min-width: 220px; z-index: 1000;'

const trigger = (type: string) => {
  colorState.value.visible = colorState.value.type === type ? !colorState.value.visible : true
  colorState.value.type = type

  if (colorState.value.visible) {
    setTimeout(() => {
      document.addEventListener('click', handleOutsideClick)
    }, 0)
  } else {
    document.removeEventListener('click', handleOutsideClick)
  }
}

const handleOutsideClick = (e: MouseEvent) => {
  const colorPickers = document.querySelectorAll('.n-color-picker')
  const colorPickerButtons = document.querySelectorAll('.n-button')

  let clickedInside = false
  colorPickers.forEach((picker) => {
    if (picker.contains(e.target as Node)) {
      clickedInside = true
    }
  })

  colorPickerButtons.forEach((button) => {
    if (
      button.contains(e.target as Node) &&
      button.querySelector('.n-icon')?.innerHTML.includes(props.colorType === 'color' ? 'TextColor24Regular' : 'Color24Regular')
    ) {
      clickedInside = true
    }
  })

  if (!clickedInside) {
    colorState.value.visible = false
    document.removeEventListener('click', handleOutsideClick)
  }
}

const handleColorChange = (color: string) => {
  if (props.colorType === 'color') {
    frequencyLimit.debounce(COLOR_PICKER, () => props.editor?.chain().focus().setColor(color).run())
  } else {
    frequencyLimit.debounce(COLOR_PICKER, () =>
      props.editor?.chain().focus().setHighlight({ color }).run(),
    )
  }
}

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick)
})
</script>

<style scoped>
:deep(.n-color-picker-trigger) {
  border: none !important;
  width: 0 !important;
  height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}
</style>
